import numpy as np
import matplotlib.pyplot as plt
from os import path

# 导入 PyFoam 中最基础的文件解析类
from PyFoam.RunDictionary.ParsedParameterFile import ParsedParameterFile

# --- 参数设置 ---
case_dir = "."
time_name = "0.0001" 
field_name = "Al" 

# --- 脚本开始 ---

# 1. 定义所有需要的文件路径
field_file_path = path.join(case_dir, time_name, field_name)
cell_centers_path = path.join(case_dir, time_name, "C")

# 2. 读取场数据
try:
    # 尝试以 ascii 模式读取
    field_parser = ParsedParameterFile(field_file_path)
    print(f"文件 '{field_name}' 是 ascii 格式。")
except Exception:
    # 如果 ascii 模式失败，立即尝试以 binary 模式读取
    print(f"文件 '{field_name}' 可能是 binary 格式，正在尝试以二进制模式读取...")
    field_parser = ParsedParameterFile(field_file_path, binaryMode=True)

# 提取数据 (同时处理均匀场和非均匀场)
if 'internalField' in field_parser.content and not isinstance(field_parser.content['internalField'], dict):
    # 非均匀场
    field_data = np.array(field_parser.content['internalField'])
else:
    # 均匀场
    field_value = field_parser.content['internalField'].value()
    # 对于均匀场，我们需要从 C 文件获取单元数
    try:
        centers_parser_for_count = ParsedParameterFile(cell_centers_path)
        num_cells = len(centers_parser_for_count.content)
        field_data = np.full(num_cells, field_value)
    except Exception as e:
        print(f"错误：场是均匀的，但无法读取 'C' 文件来确定单元数。错误: {e}")
        exit()

# print(f"成功读取场 '{field_name}' 的数据，共 {len(field_data)} 个单元。")


# 3. 读取单元中心坐标 C 文件
try:
    # 同样地，对 C 文件也进行 ascii/binary 双重尝试
    try:
        centers_parser = ParsedParameterFile(cell_centers_path)
        print(f"文件 'C' 是 ascii 格式。")
    except Exception:
        print(f"文件 'C' 可能是 binary 格式，正在尝试以二进制模式读取...")
        centers_parser = ParsedParameterFile(cell_centers_path, binaryMode=True)

    cell_centers = np.array(centers_parser.content)
    print(f"成功读取单元中心坐标文件 'C'，共 {len(cell_centers)} 个。")
except Exception as e:
    print("\n--- 关键错误 ---")
    print(f"无法读取单元中心坐标文件 '{cell_centers_path}'。错误: {e}")
    print("\n请确保您已经在 OpenFOAM 终端中，在算例目录下成功运行了以下命令：")
    print(f"postProcess -func writeCellCentres -time {time_name}")
    print("如果已经运行过，请检查 '0.0001/C' 文件是否存在且内容正确。")
    exit()

# 4. 绘图
if len(field_data) != len(cell_centers):
    print(f"错误: 场数据 ({len(field_data)} 点) 和坐标数据 ({len(cell_centers)} 点) 的数量不匹配!")
    exit()

x_coordinates = cell_centers[:, 0]

plt.figure(figsize=(10, 6))
plt.plot(x_coordinates, field_data, marker='.', linestyle='-')
plt.title(f"标量场 '{field_name}' 在 t = {time_name} 时刻的分布")
plt.xlabel("空间位置 X (m)")
plt.ylabel(f"'{field_name}' 的值")
plt.grid(True)
plt.show()

print("\n绘图完成。")