/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      binary;
    arch        "LSB;label=32;scalar=64";
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

4
(
    left
    {
        type            patch;
        nFaces          1;
        startFace       1023;
    }
    right
    {
        type            patch;
        nFaces          1;
        startFace       1024;
    }
    upAndDown
    {
        type            symmetry;
        inGroups        1(symmetry);
        nFaces          2048;
        startFace       1025;
    }
    frontAndBack
    {
        type            empty;
        inGroups        1(empty);
        nFaces          2048;
        startFace       3073;
    }
)

// ************************************************************************* //
