/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2019-2021 OpenCFD Ltd.
    Copyright (C) YEAR AUTHOR, AFFILIATION
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

\*---------------------------------------------------------------------------*/

#include "fixedValueFvPatchFieldTemplate.H"
#include "addToRunTimeSelectionTable.H"
#include "fvPatchFieldMapper.H"
#include "volFields.H"
#include "surfaceFields.H"
#include "unitConversion.H"
#include "PatchFunction1.H"

//{{{ begin codeInclude
#line 31 "/home/<USER>/test/aluminum_1D/0/Al/boundaryField/left"
#include "volFields.H"
            #include "fvPatchFields.H"
//}}} end codeInclude


// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

namespace Foam
{

// * * * * * * * * * * * * * * * Local Functions * * * * * * * * * * * * * * //

//{{{ begin localCode

//}}} end localCode


// * * * * * * * * * * * * * * * Global Functions  * * * * * * * * * * * * * //

// dynamicCode:
// SHA1 = b0ff9fdde2df5aba7aa70a729494016a3bc70a69
//
// unique function name that can be checked if the correct library version
// has been loaded
extern "C" void YalSurf_b0ff9fdde2df5aba7aa70a729494016a3bc70a69(bool load)
{
    if (load)
    {
        // Code that can be explicitly executed after loading
    }
    else
    {
        // Code that can be explicitly executed before unloading
    }
}

// * * * * * * * * * * * * * * Static Data Members * * * * * * * * * * * * * //

makeRemovablePatchTypeField
(
    fvPatchScalarField,
    YalSurfFixedValueFvPatchScalarField
);

} // End namespace Foam


// * * * * * * * * * * * * * * * * Constructors  * * * * * * * * * * * * * * //

Foam::
YalSurfFixedValueFvPatchScalarField::
YalSurfFixedValueFvPatchScalarField
(
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF
)
:
    parent_bctype(p, iF)
{
    if (false)
    {
        printMessage("Construct YalSurf : patch/DimensionedField");
    }
}


Foam::
YalSurfFixedValueFvPatchScalarField::
YalSurfFixedValueFvPatchScalarField
(
    const YalSurfFixedValueFvPatchScalarField& rhs,
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF,
    const fvPatchFieldMapper& mapper
)
:
    parent_bctype(rhs, p, iF, mapper)
{
    if (false)
    {
        printMessage("Construct YalSurf : patch/DimensionedField/mapper");
    }
}


Foam::
YalSurfFixedValueFvPatchScalarField::
YalSurfFixedValueFvPatchScalarField
(
    const fvPatch& p,
    const DimensionedField<scalar, volMesh>& iF,
    const dictionary& dict
)
:
    parent_bctype(p, iF, dict)
{
    if (false)
    {
        printMessage("Construct YalSurf : patch/dictionary");
    }
}


Foam::
YalSurfFixedValueFvPatchScalarField::
YalSurfFixedValueFvPatchScalarField
(
    const YalSurfFixedValueFvPatchScalarField& rhs
)
:
    parent_bctype(rhs),
    dictionaryContent(rhs)
{
    if (false)
    {
        printMessage("Copy construct YalSurf");
    }
}


Foam::
YalSurfFixedValueFvPatchScalarField::
YalSurfFixedValueFvPatchScalarField
(
    const YalSurfFixedValueFvPatchScalarField& rhs,
    const DimensionedField<scalar, volMesh>& iF
)
:
    parent_bctype(rhs, iF)
{
    if (false)
    {
        printMessage("Construct YalSurf : copy/DimensionedField");
    }
}


// * * * * * * * * * * * * * * * * Destructor  * * * * * * * * * * * * * * * //

Foam::
YalSurfFixedValueFvPatchScalarField::
~YalSurfFixedValueFvPatchScalarField()
{
    if (false)
    {
        printMessage("Destroy YalSurf");
    }
}


// * * * * * * * * * * * * * * * Member Functions  * * * * * * * * * * * * * //

void
Foam::
YalSurfFixedValueFvPatchScalarField::updateCoeffs()
{
    if (this->updated())
    {
        return;
    }

    if (false)
    {
        printMessage("updateCoeffs YalSurf");
    }

//{{{ begin code
    #line 37 "/home/<USER>/test/aluminum_1D/0/Al/boundaryField/left"
const scalar MF = 27.0;
            const scalar MG = 18.0;
            const scalar PF_sat = 0.81354; // 单位应为 Pa
            const scalar Patm = 1.0; // 假设大气压为标准大气压

            forAll(this->patch(), faceI)
            {
                scalar YF_s_numerator = PF_sat * MF;
                scalar YF_s_denominator = (Patm - PF_sat) * MG + PF_sat * MF;
                scalar YF_s = 0.0;
                if (YF_s_denominator > VSMALL)
                {
                    YF_s = YF_s_numerator / YF_s_denominator;
                }
                this->operator[](faceI) = YF_s;
            }
//}}} end code

    this->parent_bctype::updateCoeffs();
}


// ************************************************************************* //

