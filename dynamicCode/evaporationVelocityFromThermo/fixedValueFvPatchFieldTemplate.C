/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2019-2021 OpenCFD Ltd.
    Copyright (C) YEAR AUTHOR, AFFILIATION
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

\*---------------------------------------------------------------------------*/

#include "fixedValueFvPatchFieldTemplate.H"
#include "addToRunTimeSelectionTable.H"
#include "fvPatchFieldMapper.H"
#include "volFields.H"
#include "surfaceFields.H"
#include "unitConversion.H"
#include "PatchFunction1.H"

//{{{ begin codeInclude

//}}} end codeInclude


// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

namespace Foam
{

// * * * * * * * * * * * * * * * Local Functions * * * * * * * * * * * * * * //

//{{{ begin localCode

//}}} end localCode


// * * * * * * * * * * * * * * * Global Functions  * * * * * * * * * * * * * //

// dynamicCode:
// SHA1 = c8b950a020e7152a028d254d27b463a7809c6c5b
//
// unique function name that can be checked if the correct library version
// has been loaded
extern "C" void evaporationVelocityFromThermo_c8b950a020e7152a028d254d27b463a7809c6c5b(bool load)
{
    if (load)
    {
        // Code that can be explicitly executed after loading
    }
    else
    {
        // Code that can be explicitly executed before unloading
    }
}

// * * * * * * * * * * * * * * Static Data Members * * * * * * * * * * * * * //

makeRemovablePatchTypeField
(
    fvPatchVectorField,
    evaporationVelocityFromThermoFixedValueFvPatchVectorField
);

} // End namespace Foam


// * * * * * * * * * * * * * * * * Constructors  * * * * * * * * * * * * * * //

Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
evaporationVelocityFromThermoFixedValueFvPatchVectorField
(
    const fvPatch& p,
    const DimensionedField<vector, volMesh>& iF
)
:
    parent_bctype(p, iF)
{
    if (false)
    {
        printMessage("Construct evaporationVelocityFromThermo : patch/DimensionedField");
    }
}


Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
evaporationVelocityFromThermoFixedValueFvPatchVectorField
(
    const evaporationVelocityFromThermoFixedValueFvPatchVectorField& rhs,
    const fvPatch& p,
    const DimensionedField<vector, volMesh>& iF,
    const fvPatchFieldMapper& mapper
)
:
    parent_bctype(rhs, p, iF, mapper)
{
    if (false)
    {
        printMessage("Construct evaporationVelocityFromThermo : patch/DimensionedField/mapper");
    }
}


Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
evaporationVelocityFromThermoFixedValueFvPatchVectorField
(
    const fvPatch& p,
    const DimensionedField<vector, volMesh>& iF,
    const dictionary& dict
)
:
    parent_bctype(p, iF, dict)
{
    if (false)
    {
        printMessage("Construct evaporationVelocityFromThermo : patch/dictionary");
    }
}


Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
evaporationVelocityFromThermoFixedValueFvPatchVectorField
(
    const evaporationVelocityFromThermoFixedValueFvPatchVectorField& rhs
)
:
    parent_bctype(rhs),
    dictionaryContent(rhs)
{
    if (false)
    {
        printMessage("Copy construct evaporationVelocityFromThermo");
    }
}


Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
evaporationVelocityFromThermoFixedValueFvPatchVectorField
(
    const evaporationVelocityFromThermoFixedValueFvPatchVectorField& rhs,
    const DimensionedField<vector, volMesh>& iF
)
:
    parent_bctype(rhs, iF)
{
    if (false)
    {
        printMessage("Construct evaporationVelocityFromThermo : copy/DimensionedField");
    }
}


// * * * * * * * * * * * * * * * * Destructor  * * * * * * * * * * * * * * * //

Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::
~evaporationVelocityFromThermoFixedValueFvPatchVectorField()
{
    if (false)
    {
        printMessage("Destroy evaporationVelocityFromThermo");
    }
}


// * * * * * * * * * * * * * * * Member Functions  * * * * * * * * * * * * * //

void
Foam::
evaporationVelocityFromThermoFixedValueFvPatchVectorField::updateCoeffs()
{
    if (this->updated())
    {
        return;
    }

    if (false)
    {
        printMessage("updateCoeffs evaporationVelocityFromThermo");
    }

//{{{ begin code
    #line 30 "/home/<USER>/test/aluminum_1D/0/U/boundaryField/left"
// 铝蒸气质量分数
            const volScalarField& Y_F = db().lookupObject<volScalarField>("Al");
            const fvPatchScalarField& YFpatch = Y_F.boundaryField()[patch().index()];

            // 扩散系数场，假设你在 transportProperties 里定义了 DAl
            const volScalarField& D_field = db().lookupObject<volScalarField>("thermo:alpha");
            const fvPatchScalarField& D_patch = D_field.boundaryField()[patch().index()];

            // 法向量
            tmp<vectorField> n_tmp = patch().nf();
            const vectorField& n = n_tmp();

            const scalar PF_sat = 0.81354;
            const scalar Patm = 1.0;
            const scalar MF = 26.98;
            const scalar MG = 18.0; // 摩尔质量

            tmp<scalarField> gradY_tmp = YFpatch.snGrad();
            const scalarField& gradY = gradY_tmp();

            forAll(patch(), faceI)
            {
                scalar DF = D_patch[faceI];  // 扩散系数
                scalar YF_s_num = PF_sat * MF;
                scalar YF_s_den = (Patm - PF_sat) * MG + PF_sat * MF;
                scalar YF_s = (YF_s_den > VSMALL ? YF_s_num / YF_s_den : 0.0);

                scalar snGradYF = gradY[faceI];
                scalar u_mag = -DF * snGradYF / (1.0 - YF_s + VSMALL);

                operator[](faceI) = u_mag * n[faceI];
            }
//}}} end code

    this->parent_bctype::updateCoeffs();
}


// ************************************************************************* //

