#!/usr/bin/env python3
"""
OpenFOAM Al场数据精确可视化脚本
使用真实的几何坐标展示0.0001时刻的Al浓度分布
"""

import numpy as np
import matplotlib.pyplot as plt
from os import path
import struct
import re

def read_openfoam_binary_field(file_path):
    """读取OpenFOAM二进制场文件"""
    with open(file_path, 'rb') as f:
        content = f.read()
        
    # 解码ASCII部分以查找关键信息
    try:
        text_part = content[:2000].decode('utf-8', errors='ignore')
    except:
        text_part = str(content[:2000])
    
    # 查找数据点数量
    match = re.search(r'nonuniform List<scalar>\s+(\d+)', text_part)
    if not match:
        raise ValueError("无法找到数据点数量")
    
    num_points = int(match.group(1))
    
    # 查找二进制数据的开始位置
    paren_pos = content.find(b'(')
    if paren_pos == -1:
        raise ValueError("无法找到数据开始位置")
    
    # 二进制数据从括号后开始
    binary_start = paren_pos + 1
    
    # 读取二进制数据 (double precision, 8 bytes per value)
    binary_data = content[binary_start:binary_start + num_points * 8]
    
    # 解析为double数组
    data = struct.unpack(f'{num_points}d', binary_data)
    
    return np.array(data)

def read_openfoam_vector_field(file_path):
    """读取OpenFOAM向量场文件（如单元中心坐标）"""
    with open(file_path, 'rb') as f:
        content = f.read()
        
    # 解码ASCII部分
    try:
        text_part = content[:2000].decode('utf-8', errors='ignore')
    except:
        text_part = str(content[:2000])
    
    # 查找数据点数量
    match = re.search(r'nonuniform List<vector>\s+(\d+)', text_part)
    if not match:
        raise ValueError("无法找到向量数据点数量")
    
    num_points = int(match.group(1))
    
    # 查找二进制数据的开始位置
    paren_pos = content.find(b'(')
    if paren_pos == -1:
        raise ValueError("无法找到数据开始位置")
    
    # 二进制数据从括号后开始
    binary_start = paren_pos + 1
    
    # 读取二进制数据 (3个double per vector, 24 bytes per vector)
    binary_data = content[binary_start:binary_start + num_points * 24]
    
    # 解析为double数组
    data = struct.unpack(f'{num_points * 3}d', binary_data)
    
    # 重新整形为 (n_points, 3) 的数组
    return np.array(data).reshape(num_points, 3)

def plot_al_distribution_precise(x_coords, al_data, time_name):
    """绘制精确的Al浓度分布图"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'Al浓度场分布 (t = {time_name}) - 精确几何坐标', fontsize=16, fontweight='bold')
    
    # 子图1: 完整数据分布
    axes[0, 0].plot(x_coords, al_data, 'b-', linewidth=1.5, alpha=0.8)
    axes[0, 0].set_title('完整Al浓度分布')
    axes[0, 0].set_xlabel('X坐标 (m)')
    axes[0, 0].set_ylabel('Al浓度')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 子图2: 对数尺度
    axes[0, 1].semilogy(x_coords, al_data + 1e-40, 'r-', linewidth=1.5)
    axes[0, 1].set_title('Al浓度分布 (对数尺度)')
    axes[0, 1].set_xlabel('X坐标 (m)')
    axes[0, 1].set_ylabel('Al浓度 (对数)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3: 高浓度区域放大
    # 找到浓度大于平均值的区域
    high_conc_mask = al_data > np.mean(al_data)
    if np.any(high_conc_mask):
        high_x = x_coords[high_conc_mask]
        high_al = al_data[high_conc_mask]
        axes[1, 0].plot(high_x, high_al, 'go-', markersize=3, linewidth=1)
        axes[1, 0].set_title('高浓度区域')
    else:
        # 如果没有高浓度区域，显示前200个点
        n_zoom = min(200, len(al_data))
        axes[1, 0].plot(x_coords[:n_zoom], al_data[:n_zoom], 'go-', markersize=2, linewidth=1)
        axes[1, 0].set_title(f'局部放大 (前{n_zoom}个点)')
    
    axes[1, 0].set_xlabel('X坐标 (m)')
    axes[1, 0].set_ylabel('Al浓度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4: 梯度分析
    gradient = np.gradient(al_data, x_coords)
    axes[1, 1].plot(x_coords, gradient, 'm-', linewidth=1.5)
    axes[1, 1].set_title('Al浓度梯度')
    axes[1, 1].set_xlabel('X坐标 (m)')
    axes[1, 1].set_ylabel('dAl/dx (1/m)')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig

def analyze_al_distribution(x_coords, al_data):
    """分析Al分布特征"""
    print(f"\n{'='*60}")
    print("Al浓度分布分析")
    print(f"{'='*60}")
    
    # 基本统计
    print(f"几何信息:")
    print(f"  X坐标范围: {np.min(x_coords):.6f} ~ {np.max(x_coords):.6f} m")
    print(f"  域长度:    {np.max(x_coords) - np.min(x_coords):.6f} m")
    print(f"  网格数量:  {len(x_coords)}")
    print(f"  平均网格间距: {np.mean(np.diff(x_coords)):.6e} m")
    
    print(f"\nAl浓度统计:")
    print(f"  最小值:    {np.min(al_data):.6e}")
    print(f"  最大值:    {np.max(al_data):.6e}")
    print(f"  平均值:    {np.mean(al_data):.6e}")
    print(f"  中位数:    {np.median(al_data):.6e}")
    print(f"  标准差:    {np.std(al_data):.6e}")
    
    # 找到特征位置
    max_idx = np.argmax(al_data)
    min_idx = np.argmin(al_data)
    print(f"\n特征位置:")
    print(f"  最大值位置: x = {x_coords[max_idx]:.6f} m (索引 {max_idx})")
    print(f"  最小值位置: x = {x_coords[min_idx]:.6f} m (索引 {min_idx})")
    
    # 分析浓度分布
    threshold_high = np.max(al_data) * 0.1  # 10%最大值作为高浓度阈值
    threshold_low = np.max(al_data) * 0.01   # 1%最大值作为低浓度阈值
    
    high_conc_mask = al_data > threshold_high
    low_conc_mask = al_data < threshold_low
    
    print(f"\n浓度分布特征:")
    print(f"  高浓度区域 (>{threshold_high:.3e}): {np.sum(high_conc_mask)} 个单元")
    print(f"  低浓度区域 (<{threshold_low:.3e}): {np.sum(low_conc_mask)} 个单元")
    
    if np.any(high_conc_mask):
        high_x_range = [np.min(x_coords[high_conc_mask]), np.max(x_coords[high_conc_mask])]
        print(f"  高浓度区域X范围: {high_x_range[0]:.6f} ~ {high_x_range[1]:.6f} m")
    
    # 计算梯度信息
    gradient = np.gradient(al_data, x_coords)
    print(f"\n梯度信息:")
    print(f"  最大梯度: {np.max(gradient):.6e} 1/m")
    print(f"  最小梯度: {np.min(gradient):.6e} 1/m")
    print(f"  平均梯度: {np.mean(gradient):.6e} 1/m")

def main():
    """主函数"""
    # 参数设置
    case_dir = "."
    time_name = "0.0001"
    field_name = "Al"
    
    field_file_path = path.join(case_dir, time_name, field_name)
    cell_centers_path = path.join(case_dir, time_name, "C")
    
    try:
        print(f"正在读取Al场数据: {field_file_path}")
        al_data = read_openfoam_binary_field(field_file_path)
        print(f"成功读取 {len(al_data)} 个Al浓度数据点")
        
        print(f"正在读取单元中心坐标: {cell_centers_path}")
        cell_centers = read_openfoam_vector_field(cell_centers_path)
        print(f"成功读取 {len(cell_centers)} 个单元中心坐标")
        
        # 检查数据一致性
        if len(al_data) != len(cell_centers):
            raise ValueError(f"数据不一致: Al数据 {len(al_data)} 点, 坐标数据 {len(cell_centers)} 点")
        
        # 提取X坐标 (假设是1D问题，主要关注X方向)
        x_coordinates = cell_centers[:, 0]
        
        # 按X坐标排序
        sort_indices = np.argsort(x_coordinates)
        x_coordinates = x_coordinates[sort_indices]
        al_data = al_data[sort_indices]
        
        # 分析数据
        analyze_al_distribution(x_coordinates, al_data)
        
        # 绘制图形
        fig = plot_al_distribution_precise(x_coordinates, al_data, time_name)
        
        # 保存图片
        output_file = f"al_distribution_precise_{time_name}.png"
        fig.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存为: {output_file}")
        
        # 显示图形
        plt.show()
        
        return al_data, x_coordinates
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    al_data, x_coords = main()
