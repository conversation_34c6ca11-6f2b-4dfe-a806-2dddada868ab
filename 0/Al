/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2406                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      Al;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 0 0 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    left
    {
        type            codedFixedValue;
        value           uniform 1.0; // 初始值

        name            YalSurf;

        codeInclude
        #{
            #include "volFields.H"
            #include "fvPatchFields.H"
        #};

        code
        #{
            const scalar MF = 27.0;
            const scalar MG = 18.0;
            const scalar PF_sat = 0.81354; // 单位应为 Pa
            const scalar Patm = 1.0; // 假设大气压为标准大气压

            forAll(this->patch(), faceI)
            {
                scalar YF_s_numerator = PF_sat * MF;
                scalar YF_s_denominator = (Patm - PF_sat) * MG + PF_sat * MF;
                scalar YF_s = 0.0;
                if (YF_s_denominator > VSMALL)
                {
                    YF_s = YF_s_numerator / YF_s_denominator;
                }
                this->operator[](faceI) = YF_s;
            }
        #};
    }

    right
    {
        type            zeroGradient;
    }

    upAndDown
    {
        type            symmetry;
    }

    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
