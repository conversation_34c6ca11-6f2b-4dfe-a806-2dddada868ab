/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2406                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volVectorField;
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   uniform (0 0 0);

boundaryField
{
    left
    {
        type            codedFixedValue;
        value           uniform (0 0 0);
        name            evaporationVelocityFromThermo;

        code
        #{
            // 铝蒸气质量分数
            const volScalarField& Y_F = db().lookupObject<volScalarField>("Al");
            const fvPatchScalarField& YFpatch = Y_F.boundaryField()[patch().index()];

            // 扩散系数场，假设你在 transportProperties 里定义了 DAl
            const volScalarField& D_field = db().lookupObject<volScalarField>("thermo:alpha");
            const fvPatchScalarField& D_patch = D_field.boundaryField()[patch().index()];

            // 法向量
            tmp<vectorField> n_tmp = patch().nf();
            const vectorField& n = n_tmp();

            const scalar PF_sat = 0.81354;
            const scalar Patm = 1.0;
            const scalar MF = 26.98;
            const scalar MG = 18.0; // 摩尔质量

            tmp<scalarField> gradY_tmp = YFpatch.snGrad();
            const scalarField& gradY = gradY_tmp();

            forAll(patch(), faceI)
            {
                scalar DF = D_patch[faceI];  // 扩散系数
                scalar YF_s_num = PF_sat * MF;
                scalar YF_s_den = (Patm - PF_sat) * MG + PF_sat * MF;
                scalar YF_s = (YF_s_den > VSMALL ? YF_s_num / YF_s_den : 0.0);

                scalar snGradYF = gradY[faceI];
                scalar u_mag = -DF * snGradYF / (1.0 - YF_s + VSMALL);

                operator[](faceI) = u_mag * n[faceI];
            }
        #};
    }

    right
    {
        type            pressureInletOutletVelocity;
        value           uniform (0 0 0);
    }

    upAndDown
    {
        type            symmetry;
    }

    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
