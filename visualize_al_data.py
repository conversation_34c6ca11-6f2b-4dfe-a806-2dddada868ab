#!/usr/bin/env python3
"""
OpenFOAM Al场数据可视化脚本
展示0.0001时刻的Al浓度分布
"""

import numpy as np
import matplotlib.pyplot as plt
from os import path
import struct
import re

def read_openfoam_binary_field(file_path):
    """读取OpenFOAM二进制场文件"""
    with open(file_path, 'rb') as f:
        content = f.read()
        
    # 解码ASCII部分以查找关键信息
    try:
        text_part = content[:2000].decode('utf-8', errors='ignore')
    except:
        text_part = str(content[:2000])
    
    # 查找数据点数量
    match = re.search(r'nonuniform List<scalar>\s+(\d+)', text_part)
    if not match:
        raise ValueError("无法找到数据点数量")
    
    num_points = int(match.group(1))
    print(f"找到 {num_points} 个数据点")
    
    # 查找二进制数据的开始位置
    paren_pos = content.find(b'(')
    if paren_pos == -1:
        raise ValueError("无法找到数据开始位置")
    
    # 二进制数据从括号后开始
    binary_start = paren_pos + 1
    
    # 读取二进制数据 (double precision, 8 bytes per value)
    binary_data = content[binary_start:binary_start + num_points * 8]
    
    # 解析为double数组
    data = struct.unpack(f'{num_points}d', binary_data)
    
    return np.array(data)

def create_coordinates(num_points, length=1.0):
    """创建一维坐标系"""
    return np.linspace(0, length, num_points)

def plot_al_distribution(x_coords, al_data, time_name):
    """绘制Al浓度分布图"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Al浓度场分布 (t = {time_name})', fontsize=16, fontweight='bold')
    
    # 子图1: 完整数据分布
    axes[0, 0].plot(x_coords, al_data, 'b-', linewidth=1.5, alpha=0.8)
    axes[0, 0].set_title('完整Al浓度分布')
    axes[0, 0].set_xlabel('归一化位置')
    axes[0, 0].set_ylabel('Al浓度')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_xlim(0, 1)
    
    # 子图2: 对数尺度
    axes[0, 1].semilogy(x_coords, al_data + 1e-40, 'r-', linewidth=1.5)  # 加小值避免log(0)
    axes[0, 1].set_title('Al浓度分布 (对数尺度)')
    axes[0, 1].set_xlabel('归一化位置')
    axes[0, 1].set_ylabel('Al浓度 (对数)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_xlim(0, 1)
    
    # 子图3: 局部放大 (前200个点)
    n_zoom = min(200, len(al_data))
    axes[1, 0].plot(x_coords[:n_zoom], al_data[:n_zoom], 'go-', markersize=2, linewidth=1)
    axes[1, 0].set_title(f'局部放大 (前{n_zoom}个点)')
    axes[1, 0].set_xlabel('归一化位置')
    axes[1, 0].set_ylabel('Al浓度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 子图4: 梯度分析
    gradient = np.gradient(al_data, x_coords)
    axes[1, 1].plot(x_coords, gradient, 'm-', linewidth=1.5)
    axes[1, 1].set_title('Al浓度梯度')
    axes[1, 1].set_xlabel('归一化位置')
    axes[1, 1].set_ylabel('dAl/dx')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_xlim(0, 1)
    
    plt.tight_layout()
    return fig

def print_statistics(al_data, field_name, time_name):
    """打印数据统计信息"""
    print(f"\n{'='*50}")
    print(f"场 '{field_name}' 在 t={time_name} 的统计信息")
    print(f"{'='*50}")
    print(f"数据点数量: {len(al_data)}")
    print(f"最小值:     {np.min(al_data):.6e}")
    print(f"最大值:     {np.max(al_data):.6e}")
    print(f"平均值:     {np.mean(al_data):.6e}")
    print(f"中位数:     {np.median(al_data):.6e}")
    print(f"标准差:     {np.std(al_data):.6e}")
    print(f"方差:       {np.var(al_data):.6e}")
    
    # 找到最大值和最小值的位置
    max_idx = np.argmax(al_data)
    min_idx = np.argmin(al_data)
    print(f"\n最大值位置: 索引 {max_idx} (归一化位置 {max_idx/len(al_data):.4f})")
    print(f"最小值位置: 索引 {min_idx} (归一化位置 {min_idx/len(al_data):.4f})")

def print_sample_data(x_coords, al_data, n_samples=10):
    """打印样本数据点"""
    print(f"\n前{n_samples}个数据点:")
    for i in range(min(n_samples, len(al_data))):
        print(f"  点 {i+1:3d}: x={x_coords[i]:.4f}, Al={al_data[i]:.6e}")
    
    print(f"\n后{n_samples}个数据点:")
    for i in range(max(0, len(al_data)-n_samples), len(al_data)):
        print(f"  点 {i+1:3d}: x={x_coords[i]:.4f}, Al={al_data[i]:.6e}")

def main():
    """主函数"""
    # 参数设置
    case_dir = "."
    time_name = "0.0001"
    field_name = "Al"
    
    field_file_path = path.join(case_dir, time_name, field_name)
    
    try:
        print(f"正在读取文件: {field_file_path}")
        
        # 读取Al场数据
        al_data = read_openfoam_binary_field(field_file_path)
        print(f"成功读取场 '{field_name}' 在 t={time_name} 的数据")
        
        # 创建坐标系
        x_coordinates = create_coordinates(len(al_data))
        
        # 打印统计信息
        print_statistics(al_data, field_name, time_name)
        
        # 打印样本数据
        print_sample_data(x_coordinates, al_data)
        
        # 绘制图形
        fig = plot_al_distribution(x_coordinates, al_data, time_name)
        
        # 保存图片
        output_file = f"al_distribution_{time_name}.png"
        fig.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存为: {output_file}")
        
        # 显示图形
        plt.show()
        
        return al_data, x_coordinates
        
    except Exception as e:
        print(f"错误: {e}")
        print(f"无法读取文件 '{field_file_path}'")
        return None, None

if __name__ == "__main__":
    al_data, x_coords = main()
