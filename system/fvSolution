/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2406                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      fvSolution;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

solvers
{
    "rho.*"
    {
        solver          diagonal;
    }

    p
    {
        solver          PCG;
        preconditioner  DIC;
        tolerance       1e-7;
        relTol          1e-3;
    }

    pFinal
    {
        $p;
        relTol          0;
    }

    "(U|h|k|epsilon)"
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance       1e-7;
        relTol          1e-3;
    }

    "(U|h|k|epsilon)Final"
    {
        $U;
    }

    Yi
    {
        solver          PBiCGStab;
        preconditioner  DILU;
        tolerance       1e-8;
        relTol          1e-3;
    }
}

PIMPLE
{
    // momentumPredictor no;
    // nOuterCorrectors  1;
    // nCorrectors     2;
    // nNonOrthogonalCorrectors 0;
    // maxCo 0.5;
    momentumPredictor yes;
    nOuterCorrectors 3;
    nCorrectors 2;
    nNonOrthogonalCorrectors 0;
    pRefCell 0;
    pRefValue 0;
    maxCo 0.1;          // 最大 Courant 数
    maxDeltaT 0.000001;        // 最大时间步长
}


// ************************************************************************* //
