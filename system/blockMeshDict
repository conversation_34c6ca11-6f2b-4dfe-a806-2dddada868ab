/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  v2406                                 |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

scale   0.000001; // to um

vertices
(
    ( 137.5    -5.15625  0.0  ) // 0
    (5500.0  -206.25     0.0  ) // 1
    (5500.0   206.25     0.0  ) // 2
    ( 137.5     5.15625  0.0  ) // 3
    ( 137.5    -5.15625  0.001) // 4
    (5500.0  -206.25     0.001) // 5
    (5500.0   206.25     0.001) // 6
    ( 137.5     5.15625  0.001) // 7
);

blocks
(
    hex (  0  1  2  3  4  5  6  7)  ( 1024  1  1) simpleGrading (5 1 1) // 0
);

edges
(
);

boundary
(
    left
    {
        type patch;
        faces
        (
            (  0  3  7  4)
        );
    }
    right
    {
        type patch;
        faces
        (
            (  1  2  6  5)
        );
    }
    upAndDown
    {
        type symmetry;
        faces
        (
            (  2  3  7  6)
            (  0  1  5  4)
        );
    }
    frontAndBack
    {
        type empty;
        faces
        (
            (  0  1  2  3)
            (  4  5  6  7)
        );
    }
);

// ************************************************************************* //
