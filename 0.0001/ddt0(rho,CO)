/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2406                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      binary;
    arch        "LSB;label=32;scalar=64";
    class       volScalarField;
    location    "0.0001";
    object      ddt0(rho,CO);
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [1 -3 -1 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    left
    {
        type            calculated;
        value           uniform 0;
    }
    right
    {
        type            calculated;
        value           uniform 0;
    }
    upAndDown
    {
        type            symmetry;
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
