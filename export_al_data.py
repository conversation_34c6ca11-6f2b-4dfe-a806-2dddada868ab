#!/usr/bin/env python3
"""
导出OpenFOAM Al场数据为CSV格式
"""

import numpy as np
import pandas as pd
from os import path
import struct
import re

def read_openfoam_binary_field(file_path):
    """读取OpenFOAM二进制场文件"""
    with open(file_path, 'rb') as f:
        content = f.read()
        
    # 解码ASCII部分以查找关键信息
    try:
        text_part = content[:2000].decode('utf-8', errors='ignore')
    except:
        text_part = str(content[:2000])
    
    # 查找数据点数量
    match = re.search(r'nonuniform List<scalar>\s+(\d+)', text_part)
    if not match:
        raise ValueError("无法找到数据点数量")
    
    num_points = int(match.group(1))
    
    # 查找二进制数据的开始位置
    paren_pos = content.find(b'(')
    if paren_pos == -1:
        raise ValueError("无法找到数据开始位置")
    
    # 二进制数据从括号后开始
    binary_start = paren_pos + 1
    
    # 读取二进制数据 (double precision, 8 bytes per value)
    binary_data = content[binary_start:binary_start + num_points * 8]
    
    # 解析为double数组
    data = struct.unpack(f'{num_points}d', binary_data)
    
    return np.array(data)

def read_openfoam_vector_field(file_path):
    """读取OpenFOAM向量场文件（如单元中心坐标）"""
    with open(file_path, 'rb') as f:
        content = f.read()
        
    # 解码ASCII部分
    try:
        text_part = content[:2000].decode('utf-8', errors='ignore')
    except:
        text_part = str(content[:2000])
    
    # 查找数据点数量
    match = re.search(r'nonuniform List<vector>\s+(\d+)', text_part)
    if not match:
        raise ValueError("无法找到向量数据点数量")
    
    num_points = int(match.group(1))
    
    # 查找二进制数据的开始位置
    paren_pos = content.find(b'(')
    if paren_pos == -1:
        raise ValueError("无法找到数据开始位置")
    
    # 二进制数据从括号后开始
    binary_start = paren_pos + 1
    
    # 读取二进制数据 (3个double per vector, 24 bytes per vector)
    binary_data = content[binary_start:binary_start + num_points * 24]
    
    # 解析为double数组
    data = struct.unpack(f'{num_points * 3}d', binary_data)
    
    # 重新整形为 (n_points, 3) 的数组
    return np.array(data).reshape(num_points, 3)

def export_al_data_to_csv(case_dir=".", time_name="0.0001", field_name="Al"):
    """导出Al数据到CSV文件"""
    
    field_file_path = path.join(case_dir, time_name, field_name)
    cell_centers_path = path.join(case_dir, time_name, "C")
    
    try:
        print(f"正在读取Al场数据: {field_file_path}")
        al_data = read_openfoam_binary_field(field_file_path)
        print(f"成功读取 {len(al_data)} 个Al浓度数据点")
        
        print(f"正在读取单元中心坐标: {cell_centers_path}")
        cell_centers = read_openfoam_vector_field(cell_centers_path)
        print(f"成功读取 {len(cell_centers)} 个单元中心坐标")
        
        # 检查数据一致性
        if len(al_data) != len(cell_centers):
            raise ValueError(f"数据不一致: Al数据 {len(al_data)} 点, 坐标数据 {len(cell_centers)} 点")
        
        # 提取坐标
        x_coords = cell_centers[:, 0]
        y_coords = cell_centers[:, 1]
        z_coords = cell_centers[:, 2]
        
        # 按X坐标排序
        sort_indices = np.argsort(x_coords)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Cell_Index': np.arange(len(al_data)),
            'X_Coordinate_m': x_coords[sort_indices],
            'Y_Coordinate_m': y_coords[sort_indices],
            'Z_Coordinate_m': z_coords[sort_indices],
            'Al_Concentration': al_data[sort_indices]
        })
        
        # 添加一些计算列
        df['Al_Concentration_Log10'] = np.log10(df['Al_Concentration'] + 1e-40)  # 避免log(0)
        df['Distance_from_Start_m'] = df['X_Coordinate_m'] - df['X_Coordinate_m'].min()
        
        # 计算梯度
        gradient = np.gradient(df['Al_Concentration'].values, df['X_Coordinate_m'].values)
        df['Al_Gradient_1_per_m'] = gradient
        
        # 保存为CSV
        csv_filename = f"al_data_{time_name}.csv"
        df.to_csv(csv_filename, index=False, float_format='%.6e')
        print(f"\n数据已导出到: {csv_filename}")
        
        # 显示统计信息
        print(f"\n导出数据统计:")
        print(f"  总数据点数: {len(df)}")
        print(f"  X坐标范围: {df['X_Coordinate_m'].min():.6f} ~ {df['X_Coordinate_m'].max():.6f} m")
        print(f"  Al浓度范围: {df['Al_Concentration'].min():.6e} ~ {df['Al_Concentration'].max():.6e}")
        print(f"  平均Al浓度: {df['Al_Concentration'].mean():.6e}")
        
        # 显示前几行数据
        print(f"\n前5行数据预览:")
        print(df.head().to_string(index=False))
        
        return df
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("OpenFOAM Al场数据导出工具")
    print("="*50)
    
    # 导出数据
    df = export_al_data_to_csv()
    
    if df is not None:
        print(f"\n导出成功！")
        print(f"CSV文件包含以下列:")
        for col in df.columns:
            print(f"  - {col}")
    else:
        print("导出失败！")

if __name__ == "__main__":
    main()
